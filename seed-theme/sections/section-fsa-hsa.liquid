<section class="fsa-hsa-section">
  <div class="fsa-hsa-container">
    <div class="fsa-hsa-card">
      {%- if section.settings.title != blank -%}
        <h2 class="fsa-hsa-title">{{ section.settings.title }}</h2>
      {%- endif -%}
      
      {%- if section.settings.show_learn_more -%}
        <button class="fsa-hsa-learn-more" onclick="openFsaPopup()">
          {%- if section.settings.learn_more_icon != blank -%}
            <span class="learn-more-icon">{{ section.settings.learn_more_icon }}</span>
          {%- else -%}
           <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
              <rect x="1.20474" y="19.9544" width="18.9499" height="18.9499" rx="9.47495" transform="rotate(-90 1.20474 19.9544)" stroke="#FEFEFF" stroke-width="0.949897"/>
              <path d="M8.96289 11.7832L16.1731 7.84571" stroke="#FEFEFF" stroke-width="0.627118" stroke-linecap="round"/>
              <path d="M12.4919 11.9862L6.76469 7.79301" stroke="#FEFEFF" stroke-width="0.627118" stroke-linecap="round"/>
              <g filter="url(#filter0_d_3054_9741)">
                <ellipse cx="10.8046" cy="10.7601" rx="0.511629" ry="0.511055" fill="#FEFEFF"/>
              </g>
              <defs>
                <filter id="filter0_d_3054_9741" x="10.0705" y="10.0266" width="1.69072" height="1.68974" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feOffset dx="0.111213" dy="0.111213"/>
                  <feGaussianBlur stdDeviation="0.166819"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.286275 0 0 0 0 0.329412 0 0 0 0 0.419608 0 0 0 0.57 0"/>
                  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3054_9741"/>
                  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3054_9741" result="shape"/>
                </filter>
              </defs>
            </svg>
          {%- endif -%}
          {{ section.settings.learn_more_text | default: 'Learn more' }}
        </button>
      {%- endif -%}
    </div>
  </div>
</section>

<!-- FSA/HSA Popup -->
<div id="fsa-popup-overlay" class="fsa-popup-overlay" onclick="closeFsaPopup()">
  <div class="fsa-popup-content" onclick="event.stopPropagation()">
    <button class="fsa-popup-close" onclick="closeFsaPopup()">
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M15 5L5 15M5 5L15 15" stroke="#999999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
    
    {%- if section.settings.popup_title != blank -%}
      <h3 class="fsa-popup-title">{{ section.settings.popup_title }}</h3>
    {%- endif -%}
    
    {%- if section.settings.popup_description != blank -%}
      <p class="fsa-popup-description">{{ section.settings.popup_description }}</p>
    {%- endif -%}
    
    {%- if section.settings.popup_subtitle != blank -%}
      <h4 class="fsa-popup-subtitle">{{ section.settings.popup_subtitle }}</h4>
    {%- endif -%}
    
    {%- if section.settings.popup_content != blank -%}
      <div class="fsa-popup-text">{{ section.settings.popup_content }}</div>
    {%- endif -%}
  </div>
</div>

<style>
  :root {
    {%- assign overlay_color = section.settings.overlay_color | default: '#000000' -%}
    {%- assign overlay_opacity = section.settings.overlay_opacity | default: 80 | divided_by: 100.0 -%}
    --fsa-overlay-color: {{ overlay_color }};
    --fsa-overlay-opacity: {{ overlay_opacity }};
  }

  .shopify-section-fsa-hsa .fsa-hsa-section {
    background: #FFFFFF;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding: 40px 0;
    box-sizing: border-box;
    position: relative;
  }

  .shopify-section-fsa-hsa .fsa-hsa-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card {
    width: 100%;
    max-width: 1098px;
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 13.299px;
    background: linear-gradient(180deg, #1E306E 0%, #0575E6 100%);
    box-sizing: border-box;
    padding: 20px;
    position: relative;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card .fsa-hsa-title {
    color: #FFF;
    text-align: center;
    font-family: Lato, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 900;
    line-height: 135%;
    margin: 20px 0 12px 0;
    max-width: 734.751px;
    padding: 0;
    border: none;
    background: none;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card button.fsa-hsa-learn-more {
    background: none;
    border: none;
    color: #FFF;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 400;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
    margin: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card button.fsa-hsa-learn-more:hover,
  .shopify-section-fsa-hsa .fsa-hsa-card button.fsa-hsa-learn-more:focus,
  .shopify-section-fsa-hsa .fsa-hsa-card button.fsa-hsa-learn-more:active {
    opacity: 0.8;
    background: none;
    color: #FFF;
    box-shadow: none;
    outline: none;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card .learn-more-icon,
  .shopify-section-fsa-hsa .fsa-hsa-card .learn-more-default-icon {
    display: inline-flex;
    align-items: center;
    color: #FFF;
    flex-shrink: 0;
  }

  .shopify-section-fsa-hsa .fsa-hsa-card .learn-more-icon svg,
  .shopify-section-fsa-hsa .fsa-hsa-card .learn-more-default-icon {
    width: 16px;
    height: 16px;
  }

  /* Popup Styles */
  .shopify-section-fsa-hsa .fsa-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    padding: 20px;
    box-sizing: border-box;
  }

  .shopify-section-fsa-hsa .fsa-popup-overlay.active {
    display: flex;
  }

  .shopify-section-fsa-hsa .fsa-popup-content {
    background: #FFF;
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  }

  .shopify-section-fsa-hsa .fsa-popup-content button.fsa-popup-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  .shopify-section-fsa-hsa .fsa-popup-content button.fsa-popup-close:hover,
  .shopify-section-fsa-hsa .fsa-popup-content button.fsa-popup-close:focus {
    opacity: 0.7;
    background: none;
    box-shadow: none;
    outline: none;
  }

  .shopify-section-fsa-hsa .fsa-popup-close svg {
    display: block;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-title {
    color: #333;
    font-family: Lato, sans-serif;
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 20px 0;
    line-height: 1.3;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-description {
    color: #898989;
    font-size: 15px;
    line-height: normal;
    margin: 0 0 24px 0;
    font-family: "Open Sans", sans-serif;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-subtitle {
    color: #333;
    font-family: Lato, sans-serif;
    font-size: 20px;
    font-weight: 600;
    margin: 24px 0 16px 0;
    line-height: 1.3;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-text {
    color: #898989;
    font-size: 15px;
    line-height: normal;
    margin-top: 24px;
    font-family: "Open Sans", sans-serif;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-text p {
    margin: 0 0 16px 0;
  }

  .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-text p:last-child {
    margin-bottom: 0;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .shopify-section-fsa-hsa .fsa-hsa-section {
      padding: 30px 0;
    }

    .shopify-section-fsa-hsa .fsa-hsa-container {
      padding: 0 16px;
    }

    .shopify-section-fsa-hsa .fsa-hsa-card {
      height: auto;
      min-height: 100px;
      padding: 24px 16px;
    }

    .shopify-section-fsa-hsa .fsa-hsa-card .fsa-hsa-title {
      font-size: 20px;
      line-height: 130%;
    }

    .shopify-section-fsa-hsa .fsa-hsa-card button.fsa-hsa-learn-more {
      font-size: 14px;
    }

    .shopify-section-fsa-hsa .fsa-popup-content {
      padding: 30px 20px;
      margin: 20px;
      max-height: 85vh;
    }

    .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-title {
      font-size: 24px;
    }

    .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-description,
    .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-text {
      font-size: 14px;
    }

    .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-subtitle {
      font-size: 18px;
    }
  }

  @media (max-width: 480px) {
    .shopify-section-fsa-hsa .fsa-hsa-card {
      padding: 20px 12px;
    }

    .shopify-section-fsa-hsa .fsa-hsa-card .fsa-hsa-title {
      font-size: 18px;
    }

    .shopify-section-fsa-hsa .fsa-popup-content {
      padding: 24px 16px;
      margin: 16px;
    }

    .shopify-section-fsa-hsa .fsa-popup-content .fsa-popup-title {
      font-size: 22px;
    }
  }
</style>

<script>
  // Apply custom overlay styles
  document.addEventListener('DOMContentLoaded', function() {
    const overlay = document.getElementById('fsa-popup-overlay');
    if (overlay) {
      {%- assign overlay_color = section.settings.overlay_color | default: '#000000' -%}
      {%- assign overlay_opacity = section.settings.overlay_opacity | default: 80 | divided_by: 100.0 -%}

      // Convert hex to RGB
      function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16)
        } : null;
      }

      const rgb = hexToRgb('{{ overlay_color }}');
      if (rgb) {
        overlay.style.background = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, {{ overlay_opacity }})`;
      }
    }
  });

  function openFsaPopup() {
    document.getElementById('fsa-popup-overlay').classList.add('active');
    document.body.style.overflow = 'hidden';
  }

  function closeFsaPopup() {
    document.getElementById('fsa-popup-overlay').classList.remove('active');
    document.body.style.overflow = '';
  }

  // Close popup on Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeFsaPopup();
    }
  });
</script>

{% schema %}
{
  "name": "FSA/HSA Section",
  "class": "shopify-section-fsa-hsa",
  "settings": [
    {
      "type": "header",
      "content": "Card Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Card Title",
      "default": "Use Your FSA/HSA Dollars Before You Lose Them"
    },
    {
      "type": "checkbox",
      "id": "show_learn_more",
      "label": "Show Learn More Button",
      "default": true
    },
    {
      "type": "text",
      "id": "learn_more_text",
      "label": "Learn More Button Text",
      "default": "Learn more"
    },
    {
      "type": "html",
      "id": "learn_more_icon",
      "label": "Learn More Icon (SVG)",
      "info": "Add custom SVG icon before the Learn More text. Leave empty to use default icon."
    },
    {
      "type": "header",
      "content": "Popup Settings"
    },
    {
      "type": "text",
      "id": "popup_title",
      "label": "Popup Title",
      "default": "FSA/HSA"
    },
    {
      "type": "textarea",
      "id": "popup_description",
      "label": "Popup Description",
      "default": "FSA (Flexible Spending Account) and HSA (Health Savings Account) are financial accounts where you are setting aside pre-tax money from your paycheck to pay for health expenses that are not covered by your insurance."
    },
    {
      "type": "text",
      "id": "popup_subtitle",
      "label": "Popup Subtitle",
      "default": "How can I buy with it?"
    },
    {
      "type": "richtext",
      "id": "popup_content",
      "label": "Popup Content",
      "default": "<p><strong>With an FSA Debit Card</strong> - If you have an FSA debit card, complete your purchase as you would with any other credit card.</p><p><strong>Without an FSA Debit Card</strong> - Simply complete your order and we'll send you an itemized receipt after your purchase. Get it here.</p>"
    },
    {
      "type": "header",
      "content": "Popup Overlay Settings"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay Color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay Opacity",
      "min": 10,
      "max": 90,
      "step": 5,
      "unit": "%",
      "default": 80
    }
  ],
  "presets": [
    {
      "name": "FSA/HSA Section",
      "settings": {
        "title": "Use Your FSA/HSA Dollars Before You Lose Them",
        "show_learn_more": true,
        "learn_more_text": "Learn more",
        "popup_title": "FSA/HSA",
        "popup_description": "FSA (Flexible Spending Account) and HSA (Health Savings Account) are financial accounts where you are setting aside pre-tax money from your paycheck to pay for health expenses that are not covered by your insurance.",
        "popup_subtitle": "How can I buy with it?",
        "popup_content": "<p><strong>With an FSA Debit Card</strong> - If you have an FSA debit card, complete your purchase as you would with any other credit card.</p><p><strong>Without an FSA Debit Card</strong> - Simply complete your order and we'll send you an itemized receipt after your purchase. Get it here.</p>",
        "overlay_color": "#000000",
        "overlay_opacity": 80
      }
    }
  ]
}
{% endschema %}
