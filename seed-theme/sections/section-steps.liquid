<section class="steps-section">
  <div class="steps-container">
    {%- if section.settings.title != blank -%}
      <h2 class="steps-title">{{ section.settings.title }}</h2>
    {%- endif -%}
    
    {%- if section.blocks.size > 0 -%}
      <div class="steps-grid">
        {%- for block in section.blocks -%}
          <div class="step-card" {{ block.shopify_attributes }}>
            <div class="step-image">
              {%- if block.settings.image != blank -%}
                <img
                  src="{{ block.settings.image | image_url: width: 423 }}"
                  srcset="{{ block.settings.image | image_url: width: 200 }} 200w,
                          {{ block.settings.image | image_url: width: 300 }} 300w,
                          {{ block.settings.image | image_url: width: 423 }} 423w"
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 423px"
                  alt="{{ block.settings.image.alt | default: block.settings.step_title }}"
                  loading="lazy"
                  width="423"
                  height="280"
                >
              {%- endif -%}
            </div>
            
            <div class="step-content">
              {%- if block.settings.step_title != blank -%}
                <h3 class="step-card-title">{{ block.settings.step_title }}</h3>
              {%- endif -%}
              
              {%- if block.settings.step_description != blank -%}
                <p class="step-description">{{ block.settings.step_description }}</p>
              {%- endif -%}
            </div>
          </div>
        {%- endfor -%}
      </div>
    {%- endif -%}
    
    {%- if section.settings.show_cta_button and section.settings.cta_text != blank -%}
      <div class="steps-cta">
        <a href="{{ section.settings.cta_url | default: '#' }}" class="steps-cta-button">
          {{ section.settings.cta_text }}
        </a>
      </div>
    {%- endif -%}
  </div>
</section>

<style>
  .shopify-section-steps .steps-section {
    background: transparent;
    padding: 60px 0;
    box-sizing: border-box;
  }

  .shopify-section-steps .steps-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }

  .shopify-section-steps .steps-title {
    color: #4A4A4A;
    text-align: center;
    font-family: Merriweather, serif;
    font-size: 36px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0.2px;
    margin: 0 0 60px 0;
    padding: 0;
    border: none;
    background: none;
  }

  .shopify-section-steps .steps-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 60px;
    max-width: 1320px;
    margin-left: auto;
    margin-right: auto;
  }

  .shopify-section-steps .step-card {
    display: flex;
    height: 460px;
    max-width: 423px;
    padding: 0;
    flex-direction: column;
    align-items: center;
    border-radius: 40px;
    background: #FFF;
    box-shadow: 0 -1px 1px 0 rgba(31, 31, 31, 0.16);
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 auto;
  }

  .shopify-section-steps .step-image {
    width: 100%;
    height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 40px 40px 0 0;
    background: #BEEBFC;
    flex-shrink: 0;
  }

  .shopify-section-steps .step-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
  }

  .shopify-section-steps .step-content {
    height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px 20px;
    background: #FFFFFF;
    text-align: center;
    gap: 12px;
    box-sizing: border-box;
  }

  .shopify-section-steps .step-card-title {
    color: #475562;
    text-align: center;
    font-family: Lato, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 29px;
    letter-spacing: 0.21px;
    margin: 0;
    padding: 0;
    border: none;
    background: none;
  }

  .shopify-section-steps .step-description {
    color: #464444;
    text-align: center;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30.5px;
    letter-spacing: 0.4px;
    margin: 0;
    padding: 0;
  }

  .shopify-section-steps .steps-cta {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }

  .shopify-section-steps .steps-cta-button {
    display: flex;
    width: 279px;
    height: 51px;
    padding: 9px 20px;
    justify-content: center;
    align-items: center;
    gap: 6px;
    border-radius: 41px;
    background: #00AEF8;
    box-shadow: 0 4px 16px 0 rgba(0, 158, 224, 0.40);
    color: #FFFFFF;
    text-decoration: none;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-weight: 900;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    border: none;
    cursor: pointer;
    box-sizing: border-box;
  }

  .shopify-section-steps .steps-cta-button:hover {
    background: #33BFFF;
    box-shadow: 0 6px 20px 0 rgba(0, 158, 224, 0.60);
    color: #FFFFFF;
    text-decoration: none;
  }

  /* Mobile responsive */
  @media (max-width: 1024px) {
    .shopify-section-steps .steps-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 24px;
    }
  }

  @media (max-width: 768px) {
    .shopify-section-steps .steps-section {
      padding: 40px 0;
    }

    .shopify-section-steps .steps-container {
      padding: 0 16px;
    }

    .shopify-section-steps .steps-title {
      font-size: 28px;
      margin-bottom: 40px;
    }

    .shopify-section-steps .steps-grid {
      grid-template-columns: 1fr;
      gap: 20px;
      margin-bottom: 40px;
    }

    .shopify-section-steps .step-card {
      height: 400px;
      max-width: 100%;
    }

    .shopify-section-steps .step-image {
      height: 240px;
    }

    .shopify-section-steps .step-content {
      height: 160px;
      padding: 20px 16px;
      gap: 8px;
    }

    .shopify-section-steps .step-card-title {
      font-size: 20px;
      line-height: 24px;
    }

    .shopify-section-steps .step-description {
      font-size: 14px;
      line-height: 26px;
    }

    .shopify-section-steps .steps-cta-button {
      width: min(279px, calc(100% - 32px));
      height: 51px;
      padding: 9px 20px;
      font-size: 14px;
    }
  }

  @media (max-width: 480px) {
    .shopify-section-steps .step-card {
      height: 360px;
      border-radius: 30px;
    }

    .shopify-section-steps .step-image {
      height: 200px;
      border-radius: 30px 30px 0 0;
    }

    .shopify-section-steps .step-content {
      height: 160px;
      padding: 16px 12px;
    }

    .shopify-section-steps .steps-title {
      font-size: 24px;
    }

    .shopify-section-steps .step-card-title {
      font-size: 18px;
      line-height: 22px;
    }

    .shopify-section-steps .step-description {
      font-size: 13px;
      line-height: 24px;
    }

    .shopify-section-steps .steps-cta-button {
      width: min(279px, calc(100% - 32px));
      height: 51px;
      padding: 9px 20px;
      font-size: 14px;
    }
  }
</style>

{% schema %}
{
  "name": "Steps Section",
  "class": "shopify-section-steps",
  "settings": [
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Get Custom Orthotics in 3 Easy Steps"
    },
    {
      "type": "header",
      "content": "Call to Action Button"
    },
    {
      "type": "checkbox",
      "id": "show_cta_button",
      "label": "Show CTA Button",
      "default": true
    },
    {
      "type": "text",
      "id": "cta_text",
      "label": "Button Text",
      "default": "TAKE THE QUIZ"
    },
    {
      "type": "url",
      "id": "cta_url",
      "label": "Button URL"
    }
  ],
  "blocks": [
    {
      "type": "step",
      "name": "Step",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Step Image"
        },
        {
          "type": "text",
          "id": "step_title",
          "label": "Step Title",
          "default": "Step 1"
        },
        {
          "type": "textarea",
          "id": "step_description",
          "label": "Step Description",
          "default": "Take 50 sec towards your pain-free life and answer a few short questions."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Steps Section",
      "blocks": [
        {
          "type": "step",
          "settings": {
            "step_title": "Step 1",
            "step_description": "Take 50 sec towards your pain-free life and answer a few short questions."
          }
        },
        {
          "type": "step",
          "settings": {
            "step_title": "Step 2",
            "step_description": "Get Upstep's impression kit at your door. Imprint your feet, and send it back free of charge."
          }
        },
        {
          "type": "step",
          "settings": {
            "step_title": "Step 3",
            "step_description": "We'll create and ship your Upsteps within 12-18 business days of receiving your impression kit."
          }
        }
      ],
      "settings": {
        "title": "Get Custom Orthotics in 3 Easy Steps",
        "show_cta_button": true,
        "cta_text": "TAKE THE QUIZ",
        "cta_url": ""
      }
    }
  ]
}
{% endschema %}
